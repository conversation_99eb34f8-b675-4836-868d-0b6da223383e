#!/usr/bin/env python3

"""
简化的CPU profiling测试脚本
避免Ray集群冲突问题
"""

import os
import sys
import ray
import tempfile
import argparse
from pathlib import Path

def setup_ray_environment():
    """设置独立的Ray环境"""
    # 创建独立的临时目录
    ray_tmp_dir = tempfile.mkdtemp(prefix="ray_vidur_")
    
    # 设置环境变量
    os.environ["RAY_TMPDIR"] = ray_tmp_dir
    os.environ["RAY_DEDUP_LOGS"] = "0"
    os.environ["RAY_DISABLE_IMPORT_WARNING"] = "1"
    
    # 尝试关闭现有Ray连接
    try:
        ray.shutdown()
    except:
        pass
    
    print(f"Ray临时目录: {ray_tmp_dir}")
    return ray_tmp_dir

def run_cpu_profiling(model_path, output_dir, max_batch_size=64, tensor_parallel_workers=[1]):
    """运行CPU profiling"""
    print(f"开始CPU profiling:")
    print(f"  模型: {model_path}")
    print(f"  输出目录: {output_dir}")
    print(f"  最大batch size: {max_batch_size}")
    print(f"  张量并行度: {tensor_parallel_workers}")
    
    # 设置Ray环境
    ray_tmp_dir = setup_ray_environment()
    
    try:
        # 导入vidur模块
        from vidur.profiling.cpu_overhead.main import main as cpu_main
        
        # 构建参数
        sys.argv = [
            "cpu_profiling",
            "--output_dir", output_dir,
            "--models", model_path,
            "--max_batch_size", str(max_batch_size),
            "--num_tensor_parallel_workers"
        ] + [str(tp) for tp in tensor_parallel_workers]
        
        print(f"执行参数: {sys.argv}")
        
        # 运行profiling
        cpu_main()
        
        print("CPU profiling完成!")
        
    except Exception as e:
        print(f"CPU profiling失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理
        try:
            ray.shutdown()
        except:
            pass
        
        # 清理临时目录
        import shutil
        try:
            shutil.rmtree(ray_tmp_dir)
        except:
            pass
    
    return True

def main():
    parser = argparse.ArgumentParser(description="简化的CPU profiling测试")
    parser.add_argument("--model", required=True, help="模型路径")
    parser.add_argument("--output_dir", default="data/profiling", help="输出目录")
    parser.add_argument("--max_batch_size", type=int, default=64, help="最大batch size")
    parser.add_argument("--tensor_parallel_workers", nargs="+", type=int, default=[1], help="张量并行度列表")
    
    args = parser.parse_args()
    
    # 检查模型路径
    if not os.path.exists(args.model):
        print(f"错误: 模型路径不存在: {args.model}")
        return 1
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 运行profiling
    success = run_cpu_profiling(
        model_path=args.model,
        output_dir=args.output_dir,
        max_batch_size=args.max_batch_size,
        tensor_parallel_workers=args.tensor_parallel_workers
    )
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())