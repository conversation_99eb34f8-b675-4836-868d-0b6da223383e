#!/bin/bash

# CPU Profiling启动脚本
# 使用sarathi-serve的profiling模块测量CPU overhead

# 配置参数 - 可根据需要修改

# 输出目录 - 建议使用标准路径以便后续vidur模拟使用
# 注意：最终数据需要复制到 data/profiling/cpu_overhead/{DEVICE}/{MODEL}/ 目录
OUTPUT_DIR="data/profiling"

# 模型配置 - 支持HuggingFace模型名或本地路径
MODELS=(
    "/share_data/llm_weights/Meta-Llama-3-8B"
)
TENSOR_PARALLEL_WORKERS=(1)
MAX_BATCH_SIZE=512

# Python虚拟环境路径
PYTHON_ENV="./env/bin/python"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查虚拟环境
check_environment() {
    if [ ! -f "$PYTHON_ENV" ]; then
        print_error "Python虚拟环境不存在: $PYTHON_ENV"
        print_info "请确保虚拟环境已正确安装"
        exit 1
    fi
    print_success "Python虚拟环境检查通过"
}

# 显示配置信息
show_config() {
    print_info "=== CPU Profiling 配置信息 ==="
    echo -e "输出目录: ${YELLOW}$OUTPUT_DIR${NC}"
    echo -e "模型列表: ${YELLOW}${MODELS[@]}${NC}"
    echo -e "张量并行度: ${YELLOW}${TENSOR_PARALLEL_WORKERS[@]}${NC}"
    echo -e "最大Batch Size: ${YELLOW}$MAX_BATCH_SIZE${NC}"
    echo -e "Python环境: ${YELLOW}$PYTHON_ENV${NC}"
    echo "=================================="
}

# 构建模型参数字符串
build_models_args() {
    local models_str=""
    for model in "${MODELS[@]}"; do
        models_str="$models_str $model"
    done
    echo "$models_str"
}

# 构建张量并行度参数字符串
build_tp_args() {
    local tp_str=""
    for tp in "${TENSOR_PARALLEL_WORKERS[@]}"; do
        tp_str="$tp_str $tp"
    done
    echo "$tp_str"
}

# 清理Ray进程
cleanup_ray() {
    print_info "清理现有Ray进程..."
    
    # 尝试优雅关闭用户Ray进程
    if pgrep -f "ray.*python" > /dev/null; then
        print_info "检测到Ray进程，尝试关闭..."
        eval "$PYTHON_ENV -c 'import ray; ray.shutdown()'" 2>/dev/null || true
        sleep 2
    fi
    
    # 设置Ray临时目录避免冲突
    export RAY_TMPDIR="/tmp/ray_vidur_$$"
    mkdir -p "$RAY_TMPDIR"
    
    print_success "Ray环境清理完成"
}

# 运行CPU profiling
run_profiling() {
    local models_args=$(build_models_args)
    local tp_args=$(build_tp_args)
    
    print_info "开始运行CPU profiling..."
    print_info "这可能需要一些时间，请耐心等待..."
    
    # 清理Ray环境
    cleanup_ray
    
    # 构建完整命令
    local cmd="$PYTHON_ENV -m vidur.profiling.cpu_overhead.main"
    cmd="$cmd --output_dir $OUTPUT_DIR"
    cmd="$cmd --models$models_args"
    cmd="$cmd --num_tensor_parallel_workers$tp_args"
    cmd="$cmd --max_batch_size $MAX_BATCH_SIZE"
    
    print_info "执行命令: $cmd"
    echo ""
    
    # 设置环境变量避免Ray冲突
    export RAY_DEDUP_LOGS=0
    export RAY_DISABLE_IMPORT_WARNING=1
    
    # 执行命令
    if eval "$cmd"; then
        print_success "CPU profiling完成!"
        show_results
    else
        print_error "CPU profiling执行失败"
        print_warning "如果遇到Ray相关错误，请尝试重启系统或联系管理员"
        exit 1
    fi
}

# 显示结果信息
show_results() {
    print_info "=== 结果信息 ==="
    if [ -d "$OUTPUT_DIR" ]; then
        print_success "结果已保存到: $OUTPUT_DIR"
        
        # 查找最新的时间戳目录
        local latest_dir=$(find "$OUTPUT_DIR/cpu_overhead" -maxdepth 1 -type d -name "20*" | sort | tail -1)
        if [ -n "$latest_dir" ]; then
            print_info "最新结果目录: $latest_dir"
            
            # 显示生成的CSV文件
            local csv_files=$(find "$latest_dir" -name "*.csv" | wc -l)
            if [ $csv_files -gt 0 ]; then
                print_success "生成了 $csv_files 个CSV结果文件"
                print_info "CSV文件位置:"
                find "$latest_dir" -name "*.csv" | sed 's/^/  /'
            fi
        fi
    else
        print_warning "未找到输出目录"
    fi
}

# 清理函数
cleanup() {
    print_info "正在清理..."
    # 这里可以添加清理逻辑，如果需要的话
}

# 捕获中断信号
trap cleanup EXIT INT TERM

# 主函数
main() {
    print_info "启动CPU Profiling脚本"
    echo ""
    
    # 检查环境
    check_environment
    
    # 显示配置
    show_config

    run_profiling
}

# 脚本入口
main "$@"