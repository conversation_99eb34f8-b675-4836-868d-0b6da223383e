#!/bin/bash
# 一键检测 Ray 启动所需系统资源
# 用法: bash check_ray_env.sh

echo "=== Ray 环境检查脚本 ==="

# 1. CPU / IO 占用
echo
echo "[CPU / IO 占用]"
echo "Top 5 CPU 占用进程:"
ps -eo pid,ppid,cmd,%mem,%cpu --sort=-%cpu | head -n 6

if command -v iostat >/dev/null 2>&1; then
    echo
    echo "IO 占用 (每秒):"
    iostat -dx 1 1 | head -n 10
else
    echo "未检测到 iostat 命令，建议安装 sysstat 以查看详细 IO。"
fi

# 2. 内存与共享内存
echo
echo "[内存 / 共享内存]"
free -h
echo
df -h /dev/shm

# 3. 磁盘空间
echo
echo "[磁盘空间]"
df -h /tmp /share_data 2>/dev/null || df -h /tmp

# 4. 文件描述符
echo
echo "[文件描述符限制]"
FD_LIMIT=$(ulimit -n)
FD_USED=$(lsof | wc -l)
echo "当前文件描述符限制: $FD_LIMIT"
echo "当前已用文件描述符: $FD_USED"
if [ "$FD_USED" -gt "$((FD_LIMIT - 100))" ]; then
    echo "⚠️ 文件描述符使用接近上限，Ray 可能无法创建新连接"
fi

# 5. /tmp 权限与大小
echo
echo "[/tmp 检查]"
ls -ld /tmp
df -h /tmp

# 6. 检查是否有 Ray 残留进程
echo
echo "[Ray 残留进程]"
ps -ef | grep ray | grep -v grep || echo "无残留 Ray 进程"

echo
echo "✅ 检查完成，请根据上面输出判断是否资源不足或被占用。"
